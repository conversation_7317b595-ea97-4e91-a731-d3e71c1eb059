+{
  "name": "otvetbot-server",
  "version": "1.0.0",
  "description": "Backend сервер для расширения ОтветБот с интеграцией OpenRouter API",
  "main": "dist/index.js",
  "scripts": {
    "start": "node dist/index.js",
    "dev": "nodemon src/index.ts",
    "build": "tsc",
    "watch": "tsc -w"
  },
  "keywords": [
    "ai",
    "openrouter",
    "chrome-extension",
    "math-solver"
  ],
  "author": "ОтветБот Team",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "morgan": "^1.10.0",
    "express-rate-limit": "^7.1.5",
    "multer": "^1.4.5-lts.1",
    "axios": "^1.6.2",
    "dotenv": "^16.3.1",
    "joi": "^17.11.0",
    "sharp": "^0.33.0",
    "node-cron": "^3.0.3"
  },
  "devDependencies": {
    "@types/express": "^4.17.21",
    "@types/node": "^20.10.5",
    "@types/cors": "^2.8.17",
    "@types/morgan": "^1.9.9",
    "@types/multer": "^1.4.11",
    "@types/joi": "^17.2.3",
    "@types/sharp": "^0.32.0",
    "@types/node-cron": "^3.0.11",
    "typescript": "^5.3.3",
    "nodemon": "^3.0.2",
    "ts-node": "^10.9.2"
  }
}