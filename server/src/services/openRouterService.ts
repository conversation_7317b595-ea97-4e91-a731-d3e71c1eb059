import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { AppError } from '../middleware/errorHandler';

export interface OpenRouterRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string | Array<{
      type: 'text' | 'image_url';
      text?: string;
      image_url?: {
        url: string;
        detail?: 'low' | 'high' | 'auto';
      };
    }>;
  }>;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

export interface OpenRouterResponse {
  id: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
}

export class OpenRouterService {
  private client: AxiosInstance;
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY!;
    this.client = axios.create({
      baseURL: process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': 'https://otvetbot.extension',
        'X-Title': 'ОтветБот Chrome Extension',
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`OpenRouter Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('OpenRouter Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        console.log(`OpenRouter Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        console.error('OpenRouter Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  async solveMathProblem(problemText: string, imageBase64?: string): Promise<string> {
    try {
      const messages: OpenRouterRequest['messages'] = [
        {
          role: 'system',
          content: `Ты опытный преподаватель математики и помощник по решению задач. Твоя задача - подробно и понятно решить математическую задачу.

ИНСТРУКЦИИ:
1. Внимательно прочитай условие задачи
2. Определи тип задачи (алгебра, геометрия, арифметика и т.д.)
3. Предоставь пошаговое решение
4. Объясни каждый шаг простым языком
5. Дай окончательный ответ

ФОРМАТ ОТВЕТА:
- Используй markdown для форматирования
- Математические формулы пиши в LaTeX ($формула$ или $$формула$$)
- Структурируй ответ с заголовками
- Будь точным и понятным

Отвечай на русском языке.`
        }
      ];

      if (imageBase64) {
        messages.push({
          role: 'user',
          content: [
            {
              type: 'text',
              text: problemText || 'Решите задачу, показанную на изображении:'
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: 'high'
              }
            }
          ]
        });
      } else {
        messages.push({
          role: 'user',
          content: problemText
        });
      }

      const request: OpenRouterRequest = {
        model: 'google/gemini-pro-1.5',
        messages,
        max_tokens: 2000,
        temperature: 0.3
      };

      const response: AxiosResponse<OpenRouterResponse> = await this.client.post('/chat/completions', request);

      if (!response.data.choices || response.data.choices.length === 0) {
        throw new AppError('No response from AI model', 500);
      }

      return response.data.choices[0].message.content;
    } catch (error: any) {
      console.error('OpenRouter API Error:', error);

      if (error.response?.status === 401) {
        throw new AppError('Invalid API key', 401);
      } else if (error.response?.status === 429) {
        throw new AppError('Rate limit exceeded. Please try again later.', 429);
      } else if (error.response?.status === 402) {
        throw new AppError('Insufficient credits on OpenRouter account', 402);
      } else {
        throw new AppError(error.message || 'Failed to solve problem', 500);
      }
    }
  }

  async analyzeImage(imageBase64: string, prompt?: string): Promise<string> {
    try {
      const messages: OpenRouterRequest['messages'] = [
        {
          role: 'system',
          content: `Ты ИИ-помощник для анализа изображений. Твоя задача - внимательно изучить изображение и предоставить подробное описание или решение задачи.

ИНСТРУКЦИИ:
1. Внимательно изучи изображение
2. Определи, что на нем изображено (текст, формулы, диаграммы, схемы и т.д.)
3. Если это математическая задача - реши ее пошагово
4. Если это текст - переведи или объясни
5. Предоставь структурированный и понятный ответ

Отвечай на русском языке.`
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt || 'Проанализируйте это изображение и объясните, что на нем изображено:'
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: 'high'
              }
            }
          ]
        }
      ];

      const request: OpenRouterRequest = {
        model: 'google/gemini-pro-vision',
        messages,
        max_tokens: 1500,
        temperature: 0.2
      };

      const response: AxiosResponse<OpenRouterResponse> = await this.client.post('/chat/completions', request);

      if (!response.data.choices || response.data.choices.length === 0) {
        throw new AppError('No response from AI model', 500);
      }

      return response.data.choices[0].message.content;
    } catch (error: any) {
      console.error('OpenRouter Image Analysis Error:', error);

      if (error.response?.status === 401) {
        throw new AppError('Invalid API key', 401);
      } else if (error.response?.status === 429) {
        throw new AppError('Rate limit exceeded. Please try again later.', 429);
      } else {
        throw new AppError(error.message || 'Failed to analyze image', 500);
      }
    }
  }

  async getAvailableModels(): Promise<any[]> {
    try {
      const response = await this.client.get('/models');
      return response.data.data || [];
    } catch (error: any) {
      console.error('Failed to get available models:', error);
      throw new AppError('Failed to get available models', 500);
    }
  }
}

export const openRouterService = new OpenRouterService();