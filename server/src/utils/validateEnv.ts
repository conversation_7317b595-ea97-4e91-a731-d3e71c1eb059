import Joi from 'joi';

const envSchema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  PORT: Joi.number().default(3000),
  OPENROUTER_API_KEY: Joi.string().required().description('OpenRouter API key is required'),
  OPENROUTER_BASE_URL: Joi.string().uri().default('https://openrouter.ai/api/v1'),
  RATE_LIMIT_WINDOW_MS: Joi.number().default(900000),
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  MAX_FILE_SIZE: Joi.number().default(10485760), // 10MB
  UPLOAD_DIR: Joi.string().default('uploads'),
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info')
}).unknown();

export function validateEnv(): void {
  const { error, value } = envSchema.validate(process.env);

  if (error) {
    console.error('❌ Environment validation error:');
    console.error(error.details.map(detail => detail.message).join(', '));
    process.exit(1);
  }

  // Update process.env with defaults
  Object.assign(process.env, value);

  console.log('✅ Environment variables validated successfully');
}