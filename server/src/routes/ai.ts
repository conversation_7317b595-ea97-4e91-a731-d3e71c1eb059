import express from 'express';
import <PERSON><PERSON> from 'joi';
import { async<PERSON><PERSON><PERSON>, AppError } from '../middleware/errorHandler';
import { openRouterService } from '../services/openRouterService';
import { validateRequest } from '../middleware/validation';

const router = express.Router();

// Validation schemas
const solveTextSchema = Joi.object({
  text: Joi.string().min(1).max(5000).required(),
  options: Joi.object({
    model: Joi.string().optional(),
    temperature: Joi.number().min(0).max(2).optional(),
    maxTokens: Joi.number().min(10).max(4000).optional()
  }).optional()
});

const solveImageSchema = Joi.object({
  image: Joi.string().required(), // base64 encoded image
  text: Joi.string().max(1000).optional(),
  options: Joi.object({
    model: Joi.string().optional(),
    temperature: Joi.number().min(0).max(2).optional(),
    maxTokens: Joi.number().min(10).max(4000).optional()
  }).optional()
});

// POST /api/ai/solve-text
router.post('/solve-text',
  validateRequest(solveTextSchema),
  asyncHandler(async (req, res) => {
    const { text, options } = req.body;

    console.log('Solving text problem:', text.substring(0, 100) + '...');

    const solution = await openRouterService.solveMathProblem(text);

    res.json({
      success: true,
      data: {
        solution,
        originalText: text,
        timestamp: new Date().toISOString()
      }
    });
  })
);

// POST /api/ai/solve-image
router.post('/solve-image',
  validateRequest(solveImageSchema),
  asyncHandler(async (req, res) => {
    const { image, text, options } = req.body;

    console.log('Solving image problem with text:', text || 'No additional text');

    // Remove data URL prefix if present
    const base64Image = image.replace(/^data:image\/[a-z]+;base64,/, '');

    const solution = await openRouterService.solveMathProblem(text || '', base64Image);

    res.json({
      success: true,
      data: {
        solution,
        originalText: text,
        timestamp: new Date().toISOString()
      }
    });
  })
);

// POST /api/ai/analyze-image
router.post('/analyze-image',
  asyncHandler(async (req, res) => {
    const { image, prompt } = req.body;

    if (!image) {
      throw new AppError('Image is required', 400);
    }

    console.log('Analyzing image with prompt:', prompt || 'No prompt provided');

    // Remove data URL prefix if present
    const base64Image = image.replace(/^data:image\/[a-z]+;base64,/, '');

    const analysis = await openRouterService.analyzeImage(base64Image, prompt);

    res.json({
      success: true,
      data: {
        analysis,
        originalPrompt: prompt,
        timestamp: new Date().toISOString()
      }
    });
  })
);

// GET /api/ai/models
router.get('/models',
  asyncHandler(async (req, res) => {
    const models = await openRouterService.getAvailableModels();

    res.json({
      success: true,
      data: {
        models: models.filter(model =>
          model.id.includes('gpt') ||
          model.id.includes('claude') ||
          model.id.includes('gemini')
        ),
        total: models.length
      }
    });
  })
);

// GET /api/ai/status
router.get('/status', (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'OpenRouter AI Service',
      status: 'operational',
      features: [
        'Text problem solving',
        'Image analysis',
        'Mathematical computations',
        'Multi-language support'
      ],
      timestamp: new Date().toISOString()
    }
  });
});

export default router;