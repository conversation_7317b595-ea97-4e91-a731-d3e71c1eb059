import express from 'express';
import multer from 'multer';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs';
import { asyncHandler, AppError } from '../middleware/errorHandler';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760') // 10MB default
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new AppError('Invalid file type. Only JPEG, PNG, GIF and WebP are allowed.', 400));
    }
  }
});

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// POST /api/image/upload
router.post('/upload',
  upload.single('image'),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      throw new AppError('No image file provided', 400);
    }

    const { buffer, mimetype } = req.file;
    const timestamp = Date.now();
    const filename = `image_${timestamp}.jpg`;
    const filepath = path.join(uploadsDir, filename);

    // Process image with sharp - convert to JPEG and optimize
    await sharp(buffer)
      .jpeg({
        quality: 85,
        progressive: true
      })
      .resize(1920, 1920, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .toFile(filepath);

    // Convert to base64 for immediate use
    const optimizedBuffer = await sharp(filepath).toBuffer();
    const base64 = optimizedBuffer.toString('base64');

    res.json({
      success: true,
      data: {
        filename,
        url: `/uploads/${filename}`,
        base64,
        size: optimizedBuffer.length,
        originalSize: buffer.length,
        format: 'jpeg',
        timestamp: new Date().toISOString()
      }
    });
  })
);

// POST /api/image/process-base64
router.post('/process-base64',
  asyncHandler(async (req, res) => {
    const { image, options = {} } = req.body;

    if (!image) {
      throw new AppError('No base64 image provided', 400);
    }

    // Remove data URL prefix if present
    const base64Data = image.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');

    const {
      maxWidth = 1920,
      maxHeight = 1920,
      quality = 85,
      format = 'jpeg'
    } = options;

    // Process image with sharp
    let sharpImage = sharp(buffer);

    // Resize if needed
    sharpImage = sharpImage.resize(maxWidth, maxHeight, {
      fit: 'inside',
      withoutEnlargement: true
    });

    // Apply format and quality
    if (format === 'jpeg') {
      sharpImage = sharpImage.jpeg({ quality, progressive: true });
    } else if (format === 'png') {
      sharpImage = sharpImage.png({ quality });
    } else if (format === 'webp') {
      sharpImage = sharpImage.webp({ quality });
    }

    const processedBuffer = await sharpImage.toBuffer();
    const processedBase64 = processedBuffer.toString('base64');

    res.json({
      success: true,
      data: {
        base64: processedBase64,
        originalSize: buffer.length,
        processedSize: processedBuffer.length,
        compressionRatio: Math.round((1 - processedBuffer.length / buffer.length) * 100),
        format,
        timestamp: new Date().toISOString()
      }
    });
  })
);

// GET /api/image/info
router.get('/info/:filename',
  asyncHandler(async (req, res) => {
    const { filename } = req.params;
    const filepath = path.join(uploadsDir, filename);

    if (!fs.existsSync(filepath)) {
      throw new AppError('Image not found', 404);
    }

    const stats = fs.statSync(filepath);
    const metadata = await sharp(filepath).metadata();

    res.json({
      success: true,
      data: {
        filename,
        size: stats.size,
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        created: stats.birthtime,
        modified: stats.mtime
      }
    });
  })
);

// DELETE /api/image/:filename
router.delete('/:filename',
  asyncHandler(async (req, res) => {
    const { filename } = req.params;
    const filepath = path.join(uploadsDir, filename);

    if (!fs.existsSync(filepath)) {
      throw new AppError('Image not found', 404);
    }

    fs.unlinkSync(filepath);

    res.json({
      success: true,
      message: 'Image deleted successfully'
    });
  })
);

export default router;