import express from 'express';
import aiRoutes from './ai';
import imageRoutes from './image';

const router = express.Router();

// API version info
router.get('/', (req, res) => {
  res.json({
    name: 'ОтветБот API',
    version: '1.0.0',
    description: 'AI-powered task solving API with OpenRouter integration',
    endpoints: {
      ai: '/api/ai',
      image: '/api/image',
      health: '/health'
    }
  });
});

// Mount route modules
router.use('/ai', aiRoutes);
router.use('/image', imageRoutes);

export default router;