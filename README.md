# ОтветБот - Chrome Extension

Расширение Chrome для решения задач с помощью ИИ через OpenRouter API. Поддерживает анализ текста и изображений, создание скриншотов и получение решений от различных AI моделей.

## 🚀 Возможности

- **Скриншоты областей** - выделите область на странице для анализа
- **Загрузка изображений** - загружайте изображения с задачами
- **Текстовые задачи** - описывайте задачи текстом
- **AI решения** - получайте подробные решения через OpenRouter API
- **Поддержка LaTeX** - математические формулы в решениях
- **Экспорт результатов** - копирование, печать, шаринг

## 📁 Структура проекта

```
otvet_bot_extension/
├── extension/              # Chrome Extension
│   ├── manifest.json      # Манифест расширения
│   ├── popup.html         # Popup интерфейс
│   ├── solution.html      # Страница с решением
│   └── src/
│       ├── background/    # Background script
│       ├── content/       # Content scripts
│       ├── popup/         # Popup scripts
│       └── assets/        # Иконки и ресурсы
├── server/                # Node.js бэкенд
│   ├── package.json       # Зависимости
│   ├── tsconfig.json      # TypeScript конфигурация
│   ├── .env.example       # Пример переменных среды
│   └── src/
│       ├── index.ts       # Основной сервер
│       ├── routes/        # API маршруты
│       ├── services/      # Сервисы (OpenRouter)
│       ├── middleware/    # Middleware функции
│       └── utils/         # Утилиты
└── shared/                # Общие типы и утилиты
```

## 🛠 Установка и настройка

### 1. Настройка сервера

```bash
cd server
npm install
```

Создайте файл `.env` на основе `.env.example`:

```env
PORT=3000
NODE_ENV=development
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
```

Запустите сервер:

```bash
# Разработка
npm run dev

# Продакшн
npm run build
npm start
```

### 2. Установка расширения Chrome

1. Откройте Chrome и перейдите в `chrome://extensions/`
2. Включите "Режим разработчика"
3. Нажмите "Загрузить распакованное расширение"
4. Выберите папку `extension`
5. Расширение появится в списке установленных

### 3. Получение API ключа OpenRouter

1. Зарегистрируйтесь на [OpenRouter.ai](https://openrouter.ai)
2. Получите API ключ в настройках аккаунта
3. Добавьте ключ в `.env` файл сервера

## 📝 Использование

### Через popup расширения:

1. Нажмите на иконку расширения в панели Chrome
2. Выберите один из способов:
   - **Скриншот** - создать скриншот области
   - **Изображение** - загрузить файл
   - **Текст** - ввести задачу текстом

### Через горячие клавиши:

- `Ctrl+Shift+S` (Windows/Linux) или `Cmd+Shift+S` (Mac) - создать скриншот

### Через контекстное меню:

1. Нажмите на иконку расширения
2. Начните выделение области на странице
3. ИИ автоматически решит задачу

## 🔧 API Endpoints

Сервер предоставляет следующие API:

### AI Routes (`/api/ai`)

- `POST /solve-text` - Решение текстовых задач
- `POST /solve-image` - Решение задач по изображению
- `POST /analyze-image` - Анализ изображений
- `GET /models` - Список доступных AI моделей
- `GET /status` - Статус сервиса

### Image Routes (`/api/image`)

- `POST /upload` - Загрузка изображений
- `POST /process-base64` - Обработка base64 изображений
- `GET /info/:filename` - Информация об изображении
- `DELETE /:filename` - Удаление изображения

### Health Check

- `GET /health` - Проверка состояния сервера

## 🎯 Пример запроса

```javascript
// Решение текстовой задачи
const response = await fetch('http://localhost:3000/api/ai/solve-text', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    text: 'Решите уравнение: x² - 5x + 6 = 0'
  })
});

const result = await response.json();
console.log(result.data.solution);
```

## 🔒 Безопасность

- CORS настроен для Chrome Extensions
- Rate limiting (100 запросов в 15 минут)
- Валидация входных данных
- Безопасная обработка файлов
- Helmet.js для дополнительной защиты

## 🎨 Поддерживаемые модели ИИ

Через OpenRouter API доступны:

- **GPT-4** - Отличное качество решений
- **Claude-3** - Хорошая работа с изображениями
- **Gemini Pro** - Быстрые и точные ответы
- **Llama 2** - Открытая модель
- И многие другие...

## 🐛 Отладка

### Логи сервера:
```bash
cd server
npm run dev
```

### Логи расширения:
1. Откройте `chrome://extensions/`
2. Найдите ОтветБот
3. Нажмите "Подробности" → "Просмотр в background"
4. Откройте Console для просмотра логов

### Проверка API:
```bash
curl http://localhost:3000/health
```

## 📄 Лицензия

MIT License

## 🤝 Вклад в проект

1. Fork проекта
2. Создайте feature branch (`git checkout -b feature/amazing-feature`)
3. Commit изменения (`git commit -m 'Add amazing feature'`)
4. Push в branch (`git push origin feature/amazing-feature`)
5. Создайте Pull Request

## 📞 Поддержка

- GitHub Issues для багов и feature requests
- Email: <EMAIL>

## 🔄 Обновления

### v1.0.0
- Первый релиз
- Поддержка скриншотов и изображений
- Интеграция с OpenRouter API
- Базовый UI

### Планы на будущее:
- Поддержка больше типов задач
- Улучшенный UI/UX
- Офлайн режим
- Плагины для других браузеров