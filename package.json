{"name": "otvetbot-chrome-extension", "version": "1.0.0", "description": "Chrome Extension для решения задач с помощью ИИ через OpenRouter API", "main": "index.js", "scripts": {"install:server": "cd server && npm install", "install:all": "npm run install:server", "dev:server": "cd server && npm run dev", "build:server": "cd server && npm run build", "start:server": "cd server && npm start", "build:extension": "echo 'Extension files are ready for loading in Chrome'", "build": "npm run build:server && npm run build:extension", "dev": "npm run dev:server", "test:server": "cd server && npm test", "lint:server": "cd server && npm run lint", "clean": "rm -rf server/dist server/node_modules", "setup": "npm run install:all && cp server/.env server/.env", "docs": "echo 'See README.md for documentation'"}, "keywords": ["chrome-extension", "ai", "openrouter", "problem-solver", "math", "education", "screenshot", "image-analysis"], "author": "ОтветБот Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/otvetbot/chrome-extension.git"}, "bugs": {"url": "https://github.com/otvetbot/chrome-extension/issues"}, "homepage": "https://github.com/otvetbot/chrome-extension#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["server"]}