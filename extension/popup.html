<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ОтветБот</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 380px;
      height: 600px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      overflow: hidden;
    }

    .header {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .logo {
      font-size: 24px;
      font-weight: 700;
      color: #007AFF;
      margin-bottom: 8px;
    }

    .subtitle {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    .content {
      padding: 30px 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 120px);
    }

    .action-buttons {
      width: 100%;
      max-width: 320px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .action-btn {
      width: 100%;
      height: 56px;
      background: rgba(255, 255, 255, 0.95);
      border: none;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .action-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      background: rgba(255, 255, 255, 1);
    }

    .action-btn:active {
      transform: translateY(0);
    }

    .btn-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #007AFF, #0056CC);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      color: white;
      font-size: 16px;
    }

    .btn-text {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .status {
      margin-top: 20px;
      padding: 12px 20px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;
      text-align: center;
      font-size: 13px;
      color: #666;
      backdrop-filter: blur(10px);
    }

    .status.connected {
      color: #4CAF50;
    }

    .status.error {
      color: #f44336;
    }

    .footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16px 20px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      text-align: center;
    }

    .footer-text {
      font-size: 12px;
      color: #999;
    }

    .settings-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 8px;
      cursor: pointer;
      color: #007AFF;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .settings-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: rotate(30deg);
    }

    .shortcut-hint {
      font-size: 11px;
      color: #999;
      margin-top: 4px;
      opacity: 0.8;
    }

    .loading {
      display: none;
      margin-top: 20px;
    }

    .spinner {
      width: 24px;
      height: 24px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid #007AFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <button class="settings-btn" id="settingsBtn" title="Настройки">⚙️</button>

  <div class="header">
    <div class="logo">ОтветБот</div>
    <div class="subtitle">AI помощник для решения задач</div>
  </div>

  <div class="content">
    <div class="action-buttons">
      <button class="action-btn" id="screenshotBtn">
        <div class="btn-icon">📷</div>
        <div>
          <div class="btn-text">Сделать скриншот</div>
          <div class="shortcut-hint">Ctrl+Shift+S</div>
        </div>
      </button>

      <button class="action-btn" id="uploadBtn">
        <div class="btn-icon">📁</div>
        <div>
          <div class="btn-text">Загрузить изображение</div>
        </div>
      </button>

      <button class="action-btn" id="textBtn">
        <div class="btn-icon">✏️</div>
        <div>
          <div class="btn-text">Описать задачу</div>
        </div>
      </button>
    </div>

    <div class="loading" id="loading">
      <div class="spinner"></div>
    </div>

    <div class="status" id="status">
      Готов к работе
    </div>
  </div>

  <div class="footer">
    <div class="footer-text">
      Powered by OpenRouter API
    </div>
  </div>

  <script src="src/popup/popup.js"></script>
</body>
</html>