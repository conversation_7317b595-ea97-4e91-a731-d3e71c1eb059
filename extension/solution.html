<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Решение задачи - ОтветБот</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
      padding: 40px 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #007AFF, #0056CC);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .header p {
      font-size: 16px;
      opacity: 0.9;
    }

    .content {
      padding: 40px;
    }

    .solution {
      line-height: 1.8;
      font-size: 16px;
      color: #333;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .actions {
      margin-top: 40px;
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
      background: #007AFF;
      color: white;
    }

    .btn-primary:hover {
      background: #0056CC;
    }

    .btn-secondary {
      background: #f0f0f0;
      color: #333;
    }

    .btn-secondary:hover {
      background: #e0e0e0;
    }

    .footer {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
      border-top: 1px solid #e0e0e0;
    }

    .copy-feedback {
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-weight: 500;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      z-index: 1000;
    }

    .copy-feedback.show {
      transform: translateX(0);
    }

    .error {
      text-align: center;
      color: #d32f2f;
      font-size: 18px;
      padding: 40px;
    }

    @media (max-width: 768px) {
      .container {
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
      }

      .header {
        padding: 20px;
      }

      .header h1 {
        font-size: 24px;
      }

      .content {
        padding: 20px;
      }

      .solution {
        font-size: 15px;
      }

      .actions {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="copy-feedback" id="copyFeedback">
    Скопировано в буфер обмена!
  </div>

  <div class="container">
    <div class="header">
      <h1>ОтветБот</h1>
      <p>Решение вашей задачи готово</p>
    </div>

    <div class="content">
      <div class="solution" id="solution">
        Загрузка решения...
      </div>

      <div class="actions">
        <button class="btn btn-primary" onclick="copySolution()">
          📋 Копировать решение
        </button>
        <button class="btn btn-secondary" onclick="printSolution()">
          🖨️ Печать
        </button>
        <button class="btn btn-secondary" onclick="shareSolution()">
          📤 Поделиться
        </button>
      </div>
    </div>

    <div class="footer">
      <p>Решение создано с помощью ИИ через OpenRouter API</p>
      <p style="margin-top: 8px; font-size: 12px; opacity: 0.7;">
        Время создания: <span id="timestamp"></span>
      </p>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Get solution from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const solution = urlParams.get('solution');

      const solutionElement = document.getElementById('solution');
      const timestampElement = document.getElementById('timestamp');

      if (solution) {
        solutionElement.textContent = decodeURIComponent(solution);
      } else {
        solutionElement.innerHTML = '<div class="error">Решение не найдено</div>';
      }

      // Set timestamp
      timestampElement.textContent = new Date().toLocaleString('ru-RU');

      // Process LaTeX if present (basic replacement)
      processLatex();
    });

    function copySolution() {
      const solution = document.getElementById('solution').textContent;

      navigator.clipboard.writeText(solution).then(() => {
        showCopyFeedback();
      }).catch(err => {
        console.error('Copy failed:', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = solution;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopyFeedback();
      });
    }

    function showCopyFeedback() {
      const feedback = document.getElementById('copyFeedback');
      feedback.classList.add('show');

      setTimeout(() => {
        feedback.classList.remove('show');
      }, 3000);
    }

    function printSolution() {
      window.print();
    }

    function shareSolution() {
      const solution = document.getElementById('solution').textContent;
      const title = 'Решение задачи от ОтветБот';

      if (navigator.share) {
        navigator.share({
          title: title,
          text: solution
        }).catch(console.error);
      } else {
        // Fallback - copy to clipboard
        copySolution();
      }
    }

    function processLatex() {
      const solutionElement = document.getElementById('solution');
      let content = solutionElement.innerHTML;

      // Basic LaTeX-like formatting
      content = content.replace(/\$\$([^$]+)\$\$/g, '<div style="text-align: center; margin: 20px 0; font-size: 18px; font-weight: bold;">$1</div>');
      content = content.replace(/\$([^$]+)\$/g, '<span style="font-style: italic; background: #f0f0f0; padding: 2px 4px; border-radius: 4px;">$1</span>');

      // Bold text
      content = content.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

      // Headers
      content = content.replace(/^## (.+)$/gm, '<h3 style="margin: 20px 0 10px; color: #007AFF;">$1</h3>');
      content = content.replace(/^# (.+)$/gm, '<h2 style="margin: 25px 0 15px; color: #007AFF;">$1</h2>');

      solutionElement.innerHTML = content;
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' && !e.shiftKey) {
        if (window.getSelection().toString() === '') {
          e.preventDefault();
          copySolution();
        }
      }

      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        printSolution();
      }
    });
  </script>
</body>
</html>