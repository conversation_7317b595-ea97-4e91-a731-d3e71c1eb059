/* Content Script Styles for ОтветБот Chrome Extension */

/* Global reset for extension elements */
#otvetbot-overlay,
#otvetbot-instructions,
#otvetbot-selection-box,
#otvetbot-result-dialog,
#otvetbot-loading-dialog,
#otvetbot-solution-dialog,
#otvetbot-error-dialog {
  all: initial;
  box-sizing: border-box;
}

/* Overlay styles */
#otvetbot-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.3) !important;
  z-index: 999999 !important;
  pointer-events: none !important;
}

/* Instructions styles */
#otvetbot-instructions {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: rgba(0, 0, 0, 0.85) !important;
  color: white !important;
  padding: 12px 20px !important;
  border-radius: 8px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  z-index: 1000000 !important;
  pointer-events: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  letter-spacing: 0.3px !important;
}

/* Selection box styles */
#otvetbot-selection-box {
  position: fixed !important;
  border: 2px solid #007AFF !important;
  background: rgba(0, 122, 255, 0.1) !important;
  z-index: 1000001 !important;
  pointer-events: none !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
  border-radius: 4px !important;
}

/* Dialog base styles */
.otvetbot-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000002 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
  overflow: hidden !important;
}

/* Button styles */
.otvetbot-btn {
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  font-family: inherit !important;
}

.otvetbot-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.otvetbot-btn:active {
  transform: translateY(0) !important;
}

.otvetbot-btn-primary {
  background: #007AFF !important;
  color: white !important;
}

.otvetbot-btn-primary:hover {
  background: #0056CC !important;
}

.otvetbot-btn-secondary {
  background: #f0f0f0 !important;
  color: #333 !important;
}

.otvetbot-btn-secondary:hover {
  background: #e0e0e0 !important;
}

.otvetbot-btn-danger {
  background: #d32f2f !important;
  color: white !important;
}

.otvetbot-btn-danger:hover {
  background: #b71c1c !important;
}

/* Spinner animation */
@keyframes otvetbot-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.otvetbot-spinner {
  width: 40px !important;
  height: 40px !important;
  border: 4px solid #f0f0f0 !important;
  border-top: 4px solid #007AFF !important;
  border-radius: 50% !important;
  margin: 0 auto 20px !important;
  animation: otvetbot-spin 1s linear infinite !important;
}

/* Dialog header styles */
.otvetbot-dialog-header {
  padding: 20px !important;
  border-bottom: 1px solid #e0e0e0 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background: #fafafa !important;
}

.otvetbot-dialog-header h3 {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #333 !important;
}

.otvetbot-close-btn {
  background: none !important;
  border: none !important;
  font-size: 24px !important;
  cursor: pointer !important;
  color: #666 !important;
  padding: 0 !important;
  width: 30px !important;
  height: 30px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

.otvetbot-close-btn:hover {
  background: #f0f0f0 !important;
  color: #333 !important;
}

/* Dialog content styles */
.otvetbot-dialog-content {
  padding: 20px !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

.otvetbot-dialog-content img {
  max-width: 100% !important;
  max-height: 300px !important;
  border-radius: 8px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Dialog footer styles */
.otvetbot-dialog-footer {
  padding: 20px !important;
  border-top: 1px solid #e0e0e0 !important;
  display: flex !important;
  gap: 12px !important;
  justify-content: flex-end !important;
  background: #fafafa !important;
}

/* Solution text styles */
.otvetbot-solution-text {
  white-space: pre-wrap !important;
  line-height: 1.6 !important;
  color: #333 !important;
  font-size: 14px !important;
}

/* Error message styles */
.otvetbot-error-message {
  color: #d32f2f !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

/* Scrollbar styles for dialogs */
.otvetbot-dialog-content::-webkit-scrollbar {
  width: 6px !important;
}

.otvetbot-dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 3px !important;
}

.otvetbot-dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 3px !important;
}

.otvetbot-dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}

/* Prevent text selection during screenshot mode */
body.otvetbot-selecting {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Hide extension elements during screenshot capture */
.otvetbot-hidden {
  display: none !important;
}

/* Loading state styles */
.otvetbot-loading-text {
  text-align: center !important;
  color: #666 !important;
  font-size: 14px !important;
  margin-top: 10px !important;
}

/* Success state styles */
.otvetbot-success-indicator {
  color: #4CAF50 !important;
  font-weight: 500 !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .otvetbot-dialog {
    max-width: 95vw !important;
    margin: 10px !important;
  }

  .otvetbot-dialog-header,
  .otvetbot-dialog-content,
  .otvetbot-dialog-footer {
    padding: 15px !important;
  }

  .otvetbot-btn {
    padding: 10px 20px !important;
    font-size: 13px !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  #otvetbot-overlay {
    background: rgba(0, 0, 0, 0.8) !important;
  }

  #otvetbot-selection-box {
    border-color: #FFD700 !important;
    background: rgba(255, 215, 0, 0.2) !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .otvetbot-btn,
  .otvetbot-close-btn {
    transition: none !important;
  }

  .otvetbot-spinner {
    animation: none !important;
  }
}