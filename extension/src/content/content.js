// Content script for ОтветБот Chrome Extension

// Global state
let isOverlayVisible = false;
let isSelecting = false;
let selectionBox = null;
let startX = 0, startY = 0;
let endX = 0, endY = 0;

// Initialize content script
function init() {
  console.log('ОтветБот Content Script initialized');

  // Listen for messages from background script
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Content script received message:', message.type);

    switch (message.type) {
      case 'TOGGLE_OVERLAY':
        toggleSelectionOverlay();
        sendResponse({ success: true });
        break;

      case 'START_SELECTION':
        startScreenshotSelection();
        sendResponse({ success: true });
        break;

      case 'GET_PAGE_INFO':
        sendResponse({
          success: true,
          data: getPageInfo()
        });
        break;

      default:
        sendResponse({ success: false, error: 'Unknown message type' });
    }
  });

  // Listen for keyboard shortcuts
  document.addEventListener('keydown', handleKeydown);
}

// Handle keyboard shortcuts
function handleKeydown(event) {
  // Ctrl/Cmd + Shift + S for screenshot
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
    event.preventDefault();
    startScreenshotSelection();
  }

  // Escape to cancel selection
  if (event.key === 'Escape' && isSelecting) {
    cancelSelection();
  }
}

// Toggle screenshot selection overlay
function toggleSelectionOverlay() {
  if (isOverlayVisible) {
    hideOverlay();
  } else {
    startScreenshotSelection();
  }
}

// Start screenshot selection mode
function startScreenshotSelection() {
  if (isSelecting) return;

  console.log('Starting screenshot selection...');

  isSelecting = true;
  isOverlayVisible = true;

  // Create overlay elements
  createOverlay();
  createInstructions();

  // Add event listeners
  document.addEventListener('mousedown', handleMouseDown);
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);

  // Change cursor
  document.body.style.cursor = 'crosshair';
}

// Create dark overlay
function createOverlay() {
  const overlay = document.createElement('div');
  overlay.id = 'otvetbot-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999999;
    pointer-events: none;
  `;
  document.body.appendChild(overlay);
}

// Create selection instructions
function createInstructions() {
  const instructions = document.createElement('div');
  instructions.id = 'otvetbot-instructions';
  instructions.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    z-index: 1000000;
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  `;
  instructions.textContent = 'Выделите область для решения задачи. ESC - отмена';
  document.body.appendChild(instructions);
}

// Create selection box
function createSelectionBox() {
  if (selectionBox) return;

  selectionBox = document.createElement('div');
  selectionBox.id = 'otvetbot-selection-box';
  selectionBox.style.cssText = `
    position: fixed;
    border: 2px solid #007AFF;
    background: rgba(0, 122, 255, 0.1);
    z-index: 1000001;
    pointer-events: none;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
  `;
  document.body.appendChild(selectionBox);
}

// Handle mouse events
function handleMouseDown(event) {
  if (!isSelecting) return;

  event.preventDefault();
  startX = event.clientX;
  startY = event.clientY;

  createSelectionBox();
  updateSelectionBox(startX, startY, startX, startY);
}

function handleMouseMove(event) {
  if (!isSelecting || !selectionBox) return;

  event.preventDefault();
  endX = event.clientX;
  endY = event.clientY;

  updateSelectionBox(startX, startY, endX, endY);
}

function handleMouseUp(event) {
  if (!isSelecting) return;

  event.preventDefault();
  endX = event.clientX;
  endY = event.clientY;

  // Calculate selection area
  const area = {
    x: Math.min(startX, endX),
    y: Math.min(startY, endY),
    width: Math.abs(endX - startX),
    height: Math.abs(endY - startY)
  };

  // Minimum selection size
  if (area.width < 10 || area.height < 10) {
    console.log('Selection too small, canceling...');
    cancelSelection();
    return;
  }

  console.log('Screenshot area selected:', area);

  // Hide overlay and capture screenshot
  hideOverlay();
  captureScreenshot(area);
}

// Update selection box position and size
function updateSelectionBox(x1, y1, x2, y2) {
  if (!selectionBox) return;

  const left = Math.min(x1, x2);
  const top = Math.min(y1, y2);
  const width = Math.abs(x2 - x1);
  const height = Math.abs(y2 - y1);

  selectionBox.style.left = left + 'px';
  selectionBox.style.top = top + 'px';
  selectionBox.style.width = width + 'px';
  selectionBox.style.height = height + 'px';
}

// Capture screenshot with selected area
function captureScreenshot(area) {
  console.log('Requesting screenshot capture...');

  // Send message to background script
  chrome.runtime.sendMessage({
    type: 'CAPTURE_SCREENSHOT',
    data: { area }
  }, (response) => {
    if (response && response.success) {
      console.log('Screenshot captured successfully');
      showResultDialog(response.data.image, area);
    } else {
      console.error('Screenshot capture failed:', response?.error);
      showErrorDialog('Не удалось сделать скриншот: ' + (response?.error || 'Неизвестная ошибка'));
    }
  });
}

// Show result dialog with captured image
function showResultDialog(imageDataUrl, area) {
  const dialog = document.createElement('div');
  dialog.id = 'otvetbot-result-dialog';
  dialog.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1000002;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  dialog.innerHTML = `
    <div style="padding: 20px; border-bottom: 1px solid #e0e0e0;">
      <h3 style="margin: 0; font-size: 18px; color: #333;">Скриншот захвачен</h3>
    </div>
    <div style="padding: 20px; text-align: center;">
      <img src="${imageDataUrl}" style="max-width: 100%; max-height: 300px; border-radius: 8px; margin-bottom: 20px;" />
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="otvetbot-solve-btn" style="
          background: #007AFF;
          color: white;
          border: none;
          border-radius: 8px;
          padding: 12px 24px;
          font-size: 14px;
          cursor: pointer;
          font-weight: 500;
        ">Решить задачу</button>
        <button id="otvetbot-cancel-btn" style="
          background: #f0f0f0;
          color: #333;
          border: none;
          border-radius: 8px;
          padding: 12px 24px;
          font-size: 14px;
          cursor: pointer;
          font-weight: 500;
        ">Отмена</button>
      </div>
    </div>
  `;

  document.body.appendChild(dialog);

  // Add event listeners
  dialog.querySelector('#otvetbot-solve-btn').addEventListener('click', () => {
    solveImageProblem(imageDataUrl);
    document.body.removeChild(dialog);
  });

  dialog.querySelector('#otvetbot-cancel-btn').addEventListener('click', () => {
    document.body.removeChild(dialog);
  });

  // Close on overlay click
  dialog.addEventListener('click', (e) => {
    if (e.target === dialog) {
      document.body.removeChild(dialog);
    }
  });
}

// Solve image problem using AI
function solveImageProblem(imageDataUrl) {
  console.log('Solving image problem...');

  // Show loading dialog
  const loadingDialog = showLoadingDialog();

  chrome.runtime.sendMessage({
    type: 'SOLVE_PROBLEM',
    data: {
      type: 'image',
      image: imageDataUrl
    }
  }, (response) => {
    document.body.removeChild(loadingDialog);

    if (response && response.success) {
      console.log('Problem solved successfully');
      showSolutionDialog(response.data.solution);
    } else {
      console.error('Problem solving failed:', response?.error);
      showErrorDialog('Не удалось решить задачу: ' + (response?.error || 'Неизвестная ошибка'));
    }
  });
}

// Show loading dialog
function showLoadingDialog() {
  const dialog = document.createElement('div');
  dialog.id = 'otvetbot-loading-dialog';
  dialog.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1000002;
    padding: 40px;
    text-align: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  dialog.innerHTML = `
    <div style="margin-bottom: 20px;">
      <div style="
        width: 40px;
        height: 40px;
        border: 4px solid #f0f0f0;
        border-top: 4px solid #007AFF;
        border-radius: 50%;
        margin: 0 auto 20px;
        animation: spin 1s linear infinite;
      "></div>
      <h3 style="margin: 0; font-size: 18px; color: #333;">Решаем задачу...</h3>
      <p style="margin: 10px 0 0; color: #666; font-size: 14px;">Это может занять несколько секунд</p>
    </div>
  `;

  // Add CSS animation
  if (!document.querySelector('#otvetbot-spinner-style')) {
    const style = document.createElement('style');
    style.id = 'otvetbot-spinner-style';
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  }

  document.body.appendChild(dialog);
  return dialog;
}

// Show solution dialog
function showSolutionDialog(solution) {
  const dialog = document.createElement('div');
  dialog.id = 'otvetbot-solution-dialog';
  dialog.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1000002;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  dialog.innerHTML = `
    <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; display: flex; justify-content: space-between; align-items: center;">
      <h3 style="margin: 0; font-size: 18px; color: #333;">Решение задачи</h3>
      <button id="otvetbot-close-btn" style="
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      ">×</button>
    </div>
    <div style="padding: 20px; max-height: 60vh; overflow-y: auto;">
      <div style="white-space: pre-wrap; line-height: 1.6; color: #333;">${solution}</div>
    </div>
    <div style="padding: 20px; border-top: 1px solid #e0e0e0; text-align: right;">
      <button id="otvetbot-copy-btn" style="
        background: #007AFF;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 14px;
        cursor: pointer;
        font-weight: 500;
        margin-right: 12px;
      ">Копировать</button>
      <button id="otvetbot-done-btn" style="
        background: #f0f0f0;
        color: #333;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 14px;
        cursor: pointer;
        font-weight: 500;
      ">Готово</button>
    </div>
  `;

  document.body.appendChild(dialog);

  // Add event listeners
  dialog.querySelector('#otvetbot-close-btn').addEventListener('click', () => {
    document.body.removeChild(dialog);
  });

  dialog.querySelector('#otvetbot-copy-btn').addEventListener('click', () => {
    navigator.clipboard.writeText(solution).then(() => {
      const btn = dialog.querySelector('#otvetbot-copy-btn');
      btn.textContent = 'Скопировано!';
      setTimeout(() => {
        btn.textContent = 'Копировать';
      }, 2000);
    });
  });

  dialog.querySelector('#otvetbot-done-btn').addEventListener('click', () => {
    document.body.removeChild(dialog);
  });

  // Close on overlay click
  dialog.addEventListener('click', (e) => {
    if (e.target === dialog) {
      document.body.removeChild(dialog);
    }
  });
}

// Show error dialog
function showErrorDialog(message) {
  const dialog = document.createElement('div');
  dialog.id = 'otvetbot-error-dialog';
  dialog.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1000002;
    max-width: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  dialog.innerHTML = `
    <div style="padding: 20px; border-bottom: 1px solid #e0e0e0;">
      <h3 style="margin: 0; font-size: 18px; color: #d32f2f;">Ошибка</h3>
    </div>
    <div style="padding: 20px;">
      <p style="margin: 0; color: #333; line-height: 1.5;">${message}</p>
    </div>
    <div style="padding: 20px; border-top: 1px solid #e0e0e0; text-align: right;">
      <button id="otvetbot-error-ok-btn" style="
        background: #d32f2f;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 14px;
        cursor: pointer;
        font-weight: 500;
      ">OK</button>
    </div>
  `;

  document.body.appendChild(dialog);

  dialog.querySelector('#otvetbot-error-ok-btn').addEventListener('click', () => {
    document.body.removeChild(dialog);
  });
}

// Cancel selection
function cancelSelection() {
  console.log('Selection canceled');
  hideOverlay();
}

// Hide overlay and cleanup
function hideOverlay() {
  isSelecting = false;
  isOverlayVisible = false;

  // Remove event listeners
  document.removeEventListener('mousedown', handleMouseDown);
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);

  // Remove overlay elements
  const overlay = document.getElementById('otvetbot-overlay');
  const instructions = document.getElementById('otvetbot-instructions');
  const selectionBox = document.getElementById('otvetbot-selection-box');

  if (overlay) document.body.removeChild(overlay);
  if (instructions) document.body.removeChild(instructions);
  if (selectionBox) document.body.removeChild(selectionBox);

  // Reset variables
  selectionBox = null;

  // Reset cursor
  document.body.style.cursor = '';
}

// Get page information
function getPageInfo() {
  return {
    title: document.title,
    url: window.location.href,
    domain: window.location.hostname,
    timestamp: Date.now()
  };
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  hideOverlay();
});

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}