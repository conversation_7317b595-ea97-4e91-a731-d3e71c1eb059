// Background service worker for ОтветБот Chrome Extension

// Constants
const SERVER_URL = 'http://localhost:3000';
const SCREENSHOT_QUALITY = 0.8;

// Extension state
let isCapturing = false;
let activeTab = null;

// Install/Update handler
chrome.runtime.onInstalled.addListener((details) => {
  console.log('ОтветБот Extension installed/updated:', details.reason);

  if (details.reason === 'install') {
    // Set default settings
    chrome.storage.local.set({
      settings: {
        serverUrl: SERVER_URL,
        autoSolve: true,
        language: 'ru',
        theme: 'light'
      }
    });
  }
});

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message.type);

  switch (message.type) {
    case 'CAPTURE_SCREENSHOT':
      handleCaptureScreenshot(message.data, sendResponse);
      return true; // Keep message channel open for async response

    case 'SOLVE_PROBLEM':
      handleSolveProblem(message.data, sendResponse);
      return true;

    case 'GET_SETTINGS':
      handleGetSettings(sendResponse);
      return true;

    case 'UPDATE_SETTINGS':
      handleUpdateSettings(message.data, sendResponse);
      return true;

    case 'PING':
      sendResponse({ success: true, message: 'Background script is alive' });
      break;

    default:
      console.warn('Unknown message type:', message.type);
      sendResponse({ success: false, error: 'Unknown message type' });
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  console.log('Extension icon clicked for tab:', tab.id);

  // Send message to content script to show overlay
  chrome.tabs.sendMessage(tab.id, {
    type: 'TOGGLE_OVERLAY'
  }).catch((error) => {
    console.log('Content script not ready, injecting...');

    // Inject content script if not already present
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['src/content/content.js']
    });
  });
});

// Capture screenshot functionality
async function handleCaptureScreenshot(data, sendResponse) {
  try {
    console.log('Capturing screenshot...');

    // Get active tab
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (!tab) {
      throw new Error('No active tab found');
    }

    // Capture visible tab
    const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, {
      format: 'png',
      quality: Math.round(SCREENSHOT_QUALITY * 100)
    });

    // If area coordinates are provided, crop the image
    let processedImage = dataUrl;
    if (data && data.area) {
      processedImage = await cropImage(dataUrl, data.area);
    }

    sendResponse({
      success: true,
      data: {
        image: processedImage,
        tabId: tab.id,
        timestamp: Date.now()
      }
    });

  } catch (error) {
    console.error('Screenshot capture failed:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Solve problem using AI
async function handleSolveProblem(data, sendResponse) {
  try {
    console.log('Solving problem with AI...');

    const { text, image, type = 'text' } = data;

    // Get settings
    const settings = await getSettings();
    const serverUrl = settings.serverUrl || SERVER_URL;

    let endpoint = '';
    let payload = {};

    if (type === 'image' && image) {
      endpoint = '/api/ai/solve-image';
      payload = {
        image: image.replace(/^data:image\/[^;]+;base64,/, ''),
        text: text || ''
      };
    } else if (type === 'text' && text) {
      endpoint = '/api/ai/solve-text';
      payload = { text };
    } else {
      throw new Error('Invalid problem data provided');
    }

    // Make API request
    const response = await fetch(`${serverUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}`);
    }

    const result = await response.json();

    sendResponse({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Problem solving failed:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Get extension settings
async function handleGetSettings(sendResponse) {
  try {
    const settings = await getSettings();
    sendResponse({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Failed to get settings:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Update extension settings
async function handleUpdateSettings(data, sendResponse) {
  try {
    await chrome.storage.local.set({ settings: data });
    sendResponse({
      success: true,
      data: data
    });
  } catch (error) {
    console.error('Failed to update settings:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Utility functions
async function getSettings() {
  const result = await chrome.storage.local.get(['settings']);
  return result.settings || {
    serverUrl: SERVER_URL,
    autoSolve: true,
    language: 'ru',
    theme: 'light'
  };
}

async function cropImage(dataUrl, area) {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      const canvas = new OffscreenCanvas(area.width, area.height);
      const ctx = canvas.getContext('2d');

      ctx.drawImage(
        img,
        area.x, area.y, area.width, area.height,
        0, 0, area.width, area.height
      );

      canvas.convertToBlob({ type: 'image/png', quality: SCREENSHOT_QUALITY })
        .then(blob => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.readAsDataURL(blob);
        });
    };
    img.src = dataUrl;
  });
}

// Health check endpoint
chrome.runtime.onConnect.addListener((port) => {
  if (port.name === 'health-check') {
    port.postMessage({
      status: 'healthy',
      timestamp: Date.now()
    });
  }
});

console.log('ОтветБот Background Script loaded successfully');