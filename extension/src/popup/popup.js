// Popup script for ОтветБот Chrome Extension

document.addEventListener('DOMContentLoaded', () => {
  console.log('ОтветБот Popup loaded');

  // Get DOM elements
  const screenshotBtn = document.getElementById('screenshotBtn');
  const uploadBtn = document.getElementById('uploadBtn');
  const textBtn = document.getElementById('textBtn');
  const settingsBtn = document.getElementById('settingsBtn');
  const status = document.getElementById('status');
  const loading = document.getElementById('loading');

  // Initialize popup
  init();

  // Event listeners
  screenshotBtn.addEventListener('click', handleScreenshot);
  uploadBtn.addEventListener('click', handleUpload);
  textBtn.addEventListener('click', handleText);
  settingsBtn.addEventListener('click', handleSettings);

  async function init() {
    try {
      // Check if background script is alive
      const response = await sendMessage({ type: 'PING' });
      if (response.success) {
        updateStatus('Готов к работе', 'connected');
      } else {
        updateStatus('Ошибка подключения', 'error');
      }

      // Load user settings
      const settings = await sendMessage({ type: 'GET_SETTINGS' });
      if (settings.success) {
        console.log('Settings loaded:', settings.data);
      }
    } catch (error) {
      console.error('Initialization failed:', error);
      updateStatus('Ошибка инициализации', 'error');
    }
  }

  async function handleScreenshot() {
    try {
      updateStatus('Запуск режима скриншота...', 'loading');
      setLoading(true);

      // Get active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!tab) {
        throw new Error('Нет активной вкладки');
      }

      // Send message to content script to start selection
      await chrome.tabs.sendMessage(tab.id, { type: 'START_SELECTION' });

      updateStatus('Выделите область на странице', 'connected');

      // Close popup to allow selection
      window.close();

    } catch (error) {
      console.error('Screenshot failed:', error);
      updateStatus('Ошибка: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  }

  async function handleUpload() {
    try {
      updateStatus('Выберите изображение...', 'loading');
      setLoading(true);

      // Create file input
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';

      input.onchange = async (e) => {
        const file = e.target.files[0];
        if (!file) {
          updateStatus('Файл не выбран', 'error');
          setLoading(false);
          return;
        }

        try {
          // Read file as base64
          const base64 = await readFileAsBase64(file);

          // Solve the problem
          const result = await sendMessage({
            type: 'SOLVE_PROBLEM',
            data: {
              type: 'image',
              image: base64
            }
          });

          if (result.success) {
            updateStatus('Задача решена!', 'connected');
            showSolution(result.data.solution);
          } else {
            throw new Error(result.error);
          }

        } catch (error) {
          console.error('Upload processing failed:', error);
          updateStatus('Ошибка: ' + error.message, 'error');
        } finally {
          setLoading(false);
        }
      };

      input.click();

    } catch (error) {
      console.error('Upload failed:', error);
      updateStatus('Ошибка загрузки', 'error');
      setLoading(false);
    }
  }

  async function handleText() {
    try {
      // Create text input modal
      const textInput = await showTextInputDialog();

      if (!textInput || !textInput.trim()) {
        updateStatus('Текст не введен', 'error');
        return;
      }

      updateStatus('Решаем задачу...', 'loading');
      setLoading(true);

      // Solve the problem
      const result = await sendMessage({
        type: 'SOLVE_PROBLEM',
        data: {
          type: 'text',
          text: textInput
        }
      });

      if (result.success) {
        updateStatus('Задача решена!', 'connected');
        showSolution(result.data.solution);
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      console.error('Text processing failed:', error);
      updateStatus('Ошибка: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  }

  async function handleSettings() {
    try {
      // Open settings page (could be a new tab or modal)
      updateStatus('Открытие настроек...', 'loading');

      // For now, just show current settings
      const settings = await sendMessage({ type: 'GET_SETTINGS' });

      if (settings.success) {
        alert(`Текущие настройки:\n\nСервер: ${settings.data.serverUrl}\nЯзык: ${settings.data.language}\nАвто-решение: ${settings.data.autoSolve ? 'Вкл' : 'Выкл'}`);
      }

      updateStatus('Готов к работе', 'connected');

    } catch (error) {
      console.error('Settings failed:', error);
      updateStatus('Ошибка настроек', 'error');
    }
  }

  // Utility functions
  function sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, resolve);
    });
  }

  function updateStatus(text, type = '') {
    status.textContent = text;
    status.className = `status ${type}`;
  }

  function setLoading(isLoading) {
    loading.style.display = isLoading ? 'block' : 'none';
  }

  function readFileAsBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  function showTextInputDialog() {
    return new Promise((resolve) => {
      const text = prompt('Введите текст задачи:', '');
      resolve(text);
    });
  }

  function showSolution(solution) {
    // Create a new tab or window to show the solution
    chrome.tabs.create({
      url: chrome.runtime.getURL('solution.html') + '?solution=' + encodeURIComponent(solution)
    });
  }

  // Handle keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'S') {
      e.preventDefault();
      handleScreenshot();
    }
  });

  // Health check interval
  setInterval(async () => {
    try {
      const response = await sendMessage({ type: 'PING' });
      if (!response.success) {
        updateStatus('Потеряно соединение', 'error');
      }
    } catch (error) {
      updateStatus('Потеряно соединение', 'error');
    }
  }, 30000); // Check every 30 seconds
});