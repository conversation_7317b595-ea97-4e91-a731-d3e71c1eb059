import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import en from './locales/en/common.json';
import ru from './locales/ru/common.json';
import zh from './locales/zh/common.json';
import es from './locales/es/common.json';
import fr from './locales/fr/common.json';
import de from './locales/de/common.json';
import pt from './locales/pt/common.json';
import ja from './locales/ja/common.json';
import ar from './locales/ar/common.json';
import hi from './locales/hi/common.json';

const resources = {
  en: { common: en },
  ru: { common: ru },
  zh: { common: zh },
  es: { common: es },
  fr: { common: fr },
  de: { common: de },
  pt: { common: pt },
  ja: { common: ja },
  ar: { common: ar },
  hi: { common: hi },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    defaultNS: 'common',
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false,
    },
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
  });

export default i18n;