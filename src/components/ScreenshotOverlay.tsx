import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ScreenshotOverlayProps {
  onClose: () => void;
}

export const ScreenshotOverlay: React.FC<ScreenshotOverlayProps> = ({ onClose }) => {
  const { t } = useTranslation('common');
  const [isSelecting, setIsSelecting] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string>('');
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [endPos, setEndPos] = useState({ x: 0, y: 0 });
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [selectionRect, setSelectionRect] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);

  const overlayRef = useRef<HTMLDivElement>(null);

  const getMousePosition = (e: React.MouseEvent) => {
    if (!overlayRef.current) return { x: 0, y: 0 };
    const rect = overlayRef.current.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    const { x, y } = getMousePosition(e);
    
    // Check if clicking inside selection area for dragging
    if (selectionRect && 
        x >= selectionRect.x && 
        x <= selectionRect.x + selectionRect.width &&
        y >= selectionRect.y && 
        y <= selectionRect.y + selectionRect.height) {
      setIsDragging(true);
      setDragStart({
        x: x - selectionRect.x,
        y: y - selectionRect.y
      });
      return;
    }

    // Start new selection if clicking on overlay
    if (e.target === overlayRef.current) {
      setIsSelecting(true);
      setStartPos({ x, y });
      setSelectionRect(null);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    const { x, y } = getMousePosition(e);

    if (isSelecting) {
      setEndPos({ x, y });
    } else if (isDragging && selectionRect) {
      const newX = x - dragStart.x;
      const newY = y - dragStart.y;
      
      // Keep selection within bounds
      const maxX = (overlayRef.current?.clientWidth || 0) - selectionRect.width;
      const maxY = (overlayRef.current?.clientHeight || 0) - selectionRect.height;
      
      setSelectionRect({
        ...selectionRect,
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY))
      });
    } else if (isResizing && selectionRect) {
      const deltaX = x - startPos.x;
      const deltaY = y - startPos.y;
      
      let newRect = { ...selectionRect };
      
      switch (resizeHandle) {
        case 'nw': // Top-left
          newRect.x += deltaX;
          newRect.y += deltaY;
          newRect.width -= deltaX;
          newRect.height -= deltaY;
          break;
        case 'n': // Top
          newRect.y += deltaY;
          newRect.height -= deltaY;
          break;
        case 'ne': // Top-right
          newRect.y += deltaY;
          newRect.width += deltaX;
          newRect.height -= deltaY;
          break;
        case 'e': // Right
          newRect.width += deltaX;
          break;
        case 'se': // Bottom-right
          newRect.width += deltaX;
          newRect.height += deltaY;
          break;
        case 's': // Bottom
          newRect.height += deltaY;
          break;
        case 'sw': // Bottom-left
          newRect.x += deltaX;
          newRect.width -= deltaX;
          newRect.height += deltaY;
          break;
        case 'w': // Left
          newRect.x += deltaX;
          newRect.width -= deltaX;
          break;
      }
      
      // Ensure minimum size
      if (newRect.width >= 20 && newRect.height >= 20) {
        setSelectionRect(newRect);
        setStartPos({ x, y });
      }
    }
  };

  const handleMouseUp = () => {
    if (isSelecting) {
      const x = Math.min(startPos.x, endPos.x);
      const y = Math.min(startPos.y, endPos.y);
      const width = Math.abs(endPos.x - startPos.x);
      const height = Math.abs(endPos.y - startPos.y);
      
      if (width > 20 && height > 20) {
        setSelectionRect({ x, y, width, height });
      }
      setIsSelecting(false);
    }
    
    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle('');
  };

  const renderResizeHandles = () => {
    if (!selectionRect) return null;
    
    const handles = [
      { position: 'nw', x: -4, y: -4, cursor: 'nw-resize' },
      { position: 'n', x: selectionRect.width / 2 - 4, y: -4, cursor: 'n-resize' },
      { position: 'ne', x: selectionRect.width - 4, y: -4, cursor: 'ne-resize' },
      { position: 'e', x: selectionRect.width - 4, y: selectionRect.height / 2 - 4, cursor: 'e-resize' },
      { position: 'se', x: selectionRect.width - 4, y: selectionRect.height - 4, cursor: 'se-resize' },
      { position: 's', x: selectionRect.width / 2 - 4, y: selectionRect.height - 4, cursor: 's-resize' },
      { position: 'sw', x: -4, y: selectionRect.height - 4, cursor: 'sw-resize' },
      { position: 'w', x: -4, y: selectionRect.height / 2 - 4, cursor: 'w-resize' }
    ];
    
    const handleMouseDownOnHandle = (e: React.MouseEvent, position: string) => {
      e.preventDefault();
      e.stopPropagation();
      
      const { x, y } = getMousePosition(e);
      setIsResizing(true);
      setResizeHandle(position);
      setStartPos({ x, y });
    };
    
    return handles.map(handle => (
      <div
        key={handle.position}
        data-handle={handle.position}
        className="absolute w-2 h-2 bg-white border-2 border-primary rounded-full z-20 hover:scale-125 transition-transform"
        style={{
          left: handle.x,
          top: handle.y,
          cursor: handle.cursor
        }}
        onMouseDown={e => handleMouseDownOnHandle(e, handle.position)}
      />
    ));
  };

  const handleSolve = () => {
    // Handle screenshot and solve logic
    console.log('Screenshot area:', selectionRect);
    onClose();
  };

  const renderSelection = () => {
    const currentRect = isSelecting ? {
      x: Math.min(startPos.x, endPos.x),
      y: Math.min(startPos.y, endPos.y),
      width: Math.abs(endPos.x - startPos.x),
      height: Math.abs(endPos.y - startPos.y)
    } : selectionRect;
    
    if (!currentRect || !overlayRef.current) return null;
    
    const overlayWidth = overlayRef.current.clientWidth;
    const overlayHeight = overlayRef.current.clientHeight;
    
    return (
      <>
        {/* Four darkened areas around selection */}
        {/* Top */}
        <div 
          className="absolute bg-black/40 pointer-events-none"
          style={{
            left: 0,
            top: 0,
            width: overlayWidth,
            height: currentRect.y
          }}
        />
        
        {/* Bottom */}
        <div 
          className="absolute bg-black/40 pointer-events-none"
          style={{
            left: 0,
            top: currentRect.y + currentRect.height,
            width: overlayWidth,
            height: overlayHeight - (currentRect.y + currentRect.height)
          }}
        />
        
        {/* Left */}
        <div 
          className="absolute bg-black/40 pointer-events-none"
          style={{
            left: 0,
            top: currentRect.y,
            width: currentRect.x,
            height: currentRect.height
          }}
        />
        
        {/* Right */}
        <div 
          className="absolute bg-black/40 pointer-events-none"
          style={{
            left: currentRect.x + currentRect.width,
            top: currentRect.y,
            width: overlayWidth - (currentRect.x + currentRect.width),
            height: currentRect.height
          }}
        />
        
        {/* Selection frame */}
        <div
          className="absolute border-2 border-primary bg-transparent pointer-events-auto"
          style={{
            left: currentRect.x,
            top: currentRect.y,
            width: currentRect.width,
            height: currentRect.height,
            cursor: isDragging ? 'grabbing' : (selectionRect && !isSelecting ? 'grab' : 'default')
          }}
        >
          {/* Resize handles */}
          {selectionRect && !isSelecting && renderResizeHandles()}
        </div>
        
        {/* Action buttons */}
        {selectionRect && !isSelecting && (
          <div
            className="absolute flex items-center space-x-3 mt-3 pointer-events-auto"
            style={{
              left: selectionRect.x + selectionRect.width / 2,
              top: selectionRect.y + selectionRect.height,
              transform: 'translateX(-50%)',
            }}
          >
            <Button
              onClick={onClose}
              size="sm"
              variant="secondary"
              className="h-9 px-4 bg-button-secondary text-foreground font-medium text-sm rounded-lg hover-lift hover:bg-button-hover transition-all duration-200"
            >
              <X className="w-4 h-4 mr-2" />
              {t('actions.cancel')}
            </Button>
            <Button
              onClick={handleSolve}
              size="sm"
              className="h-9 px-4 gradient-primary text-white font-medium text-sm rounded-lg hover-lift shadow-lg transition-all duration-200"
            >
              <Check className="w-4 h-4 mr-2" />
              {t('actions.solve')}
            </Button>
          </div>
        )}
      </>
    );
  };

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 z-50 cursor-crosshair"
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      style={{ pointerEvents: 'auto' }}
    >
      {renderSelection()}
      
      {/* Escape hint */}
      {!selectionRect && (
        <div className="absolute top-8 left-1/2 transform -translate-x-1/2">
          <div className="glass-effect px-4 py-2 rounded-lg">
            <p className="text-sm text-foreground font-medium">
              {t('hints.selectAreaForScreenshot')}
            </p>
            <p className="text-xs text-text-secondary mt-1">
              {t('hints.pressEscToCancel')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};