import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Camera, Upload, Edit3, Copy, FileDown, X, Check, ArrowLeft, Send } from 'lucide-react';
import mathIllustration from '@/assets/math-illustration.png';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScreenshotOverlay } from './ScreenshotOverlay';
import { LanguageSelector } from './LanguageSelector';
import { ProgressIndicator } from './ProgressIndicator';
import { ImagePreview } from './ImagePreview';

type Status = 'idle' | 'solving' | 'complete' | 'error';
type SolvingStage = 'analyzing' | 'processing' | 'generating' | 'complete';

export const AnswerBotPanel = () => {
  const { t } = useTranslation('common');
  const [showTextarea, setShowTextarea] = useState(false);
  const [showOverlay, setShowOverlay] = useState(false);
  const [taskText, setTaskText] = useState('');
  const [status, setStatus] = useState<Status>('idle');
  const [solvingStage, setSolvingStage] = useState<SolvingStage>('analyzing');
  const [progress, setProgress] = useState(0);
  const [answer, setAnswer] = useState('');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const handleScreenshot = () => {
    setShowOverlay(true);
  };

  const handleFileUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setUploadedImage(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  const handleWriteTask = () => {
    setShowTextarea(true);
  };

  const handleSolve = () => {
    if (!taskText.trim() && !uploadedImage) return;
    
    setStatus('solving');
    setProgress(0);
    setSolvingStage('analyzing');
    
    // Simulate progressive solving process
    const stages: SolvingStage[] = ['analyzing', 'processing', 'generating', 'complete'];
    let currentStageIndex = 0;
    
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 15 + 5;
        if (newProgress >= 100) {
          clearInterval(progressInterval);
          setStatus('complete');
          setAnswer(t('examples.quadraticEquation'));
          return 100;
        }
        
        // Update stage based on progress
        const stageProgress = Math.floor(newProgress / 25);
        if (stageProgress > currentStageIndex && stageProgress < stages.length) {
          currentStageIndex = stageProgress;
          setSolvingStage(stages[currentStageIndex]);
        }
        
        return newProgress;
      });
    }, 300);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(answer);
  };

  const handleSignIn = () => {
    // TODO: Implement sign in functionality
    console.log('Sign in clicked');
  };

  const getStatusText = () => {
    switch (status) {
      case 'solving': return t('status.solving');
      case 'complete': return t('status.complete');
      case 'error': return t('status.error');
      default: return '';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'solving': return 'text-status-warning';
      case 'complete': return 'text-status-success';
      case 'error': return 'text-status-error';
      default: return '';
    }
  };

  return (
    <>
      <div className="w-[380px] h-screen bg-panel border-l border-card-border flex flex-col font-inter">
        {/* Header */}
        <div className="flex-shrink-0 px-4 py-6 border-b border-card-border bg-gradient-to-r from-panel to-glass-bg">
          <div className="flex items-center justify-between">
            <LanguageSelector />
            <div className="flex items-center space-x-3">
              <div className="glass-effect px-3 py-1.5 rounded-full">
                <span className="text-xs text-text-secondary font-medium">
                  {t('app.plan', { count: 15 })}
                </span>
              </div>
              <Button
                onClick={handleSignIn}
                size="sm"
                className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center p-0 hover:scale-105 transition-transform duration-200"
              >
                <span className="text-xs text-white font-semibold">A</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col p-4 space-y-4 overflow-y-auto">
          {/* Action Buttons */}
          {!showTextarea && (
            <div className="flex flex-col items-center justify-center flex-1 px-2">
              <div className="w-full max-w-[320px] space-y-4">
                <Button
                  onClick={handleScreenshot}
                  className="w-full h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium text-base rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 flex items-center justify-start pl-6 relative"
                >
                  <div className="w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center mr-4">
                    <Camera className="w-5 h-5" />
                  </div>
                  {t('actions.takeScreenshot')}
                  <div className="absolute bottom-2 right-3 bg-white/20 px-2 py-1 rounded-md text-xs font-medium">
                    ⌘S
                  </div>
                </Button>

                <Button
                  onClick={handleFileUpload}
                  className="w-full h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium text-base rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 flex items-center justify-start pl-6"
                >
                  <div className="w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center mr-4">
                    <Upload className="w-5 h-5" />
                  </div>
                  {t('actions.uploadImage')}
                </Button>

                <Button
                  onClick={handleWriteTask}
                  className="w-full h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium text-base rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 flex items-center justify-start pl-6"
                >
                  <div className="w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center mr-4">
                    <Edit3 className="w-5 h-5" />
                  </div>
                  {t('actions.writeTask')}
                </Button>
              </div>
            </div>
          )}

          {/* Uploaded Image Preview */}
          {uploadedImage && (
            <ImagePreview 
              src={uploadedImage} 
              onRemove={() => setUploadedImage(null)}
              onRetake={() => {
                setUploadedImage(null);
                handleFileUpload();
              }}
            />
          )}

          {/* Text Input (appears after clicking "Write Task") */}
          {showTextarea && (
            <div className="flex-1 flex flex-col items-center justify-center px-4 fade-up">
              {/* Illustration */}
              <div className="mb-6">
                <img 
                  src={mathIllustration} 
                  alt="Math solving illustration" 
                  className="w-64 h-56 object-contain"
                />
              </div>
              
              {/* Input area */}
              <div className="w-full max-w-[320px] space-y-4">
                <div className="relative">
                  <Textarea
                    placeholder={t('placeholders.describeTask')}
                    value={taskText}
                    onChange={(e) => setTaskText(e.target.value)}
                    className="min-h-[60px] bg-white border border-gray-200 text-gray-900 placeholder:text-gray-500 rounded-2xl resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 pr-12 text-center"
                  />
                  <Button
                    onClick={handleSolve}
                    disabled={(!taskText.trim() && !uploadedImage) || status === 'solving'}
                    size="sm"
                    className="absolute right-2 top-2 h-8 w-8 bg-blue-500 hover:bg-blue-600 text-white rounded-xl p-0 disabled:opacity-50"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="flex space-x-3">
                  <Button
                    onClick={() => setShowTextarea(false)}
                    variant="secondary"
                    className="flex-1 h-12 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-2xl font-medium"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    {t('actions.back')}
                  </Button>
                  <Button
                    onClick={handleSolve}
                    disabled={(!taskText.trim() && !uploadedImage) || status === 'solving'}
                    className="flex-1 h-12 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl font-medium disabled:opacity-50"
                  >
                    {status === 'solving' ? t('actions.solving') : t('actions.solve')}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Status & Progress */}
          {status === 'solving' && (
            <div className="space-y-3 fade-up">
              <ProgressIndicator stage={solvingStage} progress={progress} />
            </div>
          )}
          
          {status === 'complete' && (
            <div className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </div>
          )}
          
          {status === 'error' && (
            <div className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </div>
          )}

          {/* Answer Block */}
          {answer && (
            <div className="glass-effect rounded-xl p-4 space-y-4 fade-up">
              <div className="prose prose-invert prose-sm max-w-none">
                <div className="text-foreground text-sm leading-relaxed whitespace-pre-wrap">
                  {answer}
                </div>
              </div>

              <div className="flex space-x-2 pt-2 border-t border-glass-border">
                <Button
                  onClick={handleCopy}
                  size="sm"
                  variant="secondary"
                  className="flex-1 h-9 glass-effect text-foreground font-medium text-xs rounded-lg hover-lift hover:bg-button-hover transition-all duration-200"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  {t('actions.copy')}
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  disabled
                  className="flex-1 h-9 glass-effect text-text-secondary font-medium text-xs rounded-lg opacity-50"
                >
                  <FileDown className="w-4 h-4 mr-2" />
                  {t('actions.export')}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Footer Hint */}
        <div className="flex-shrink-0 p-4 border-t border-card-border">
          <div className="text-xs text-text-secondary text-center leading-relaxed">
            {t('hints.selectArea')}
          </div>
        </div>
      </div>

      {/* Screenshot Overlay */}
      {showOverlay && (
        <ScreenshotOverlay onClose={() => setShowOverlay(false)} />
      )}
    </>
  );
};