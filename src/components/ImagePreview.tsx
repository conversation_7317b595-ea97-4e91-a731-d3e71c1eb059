import React from 'react';
import { X, RotateCcw, ZoomIn, ZoomOut } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

interface ImagePreviewProps {
  src: string;
  onRemove: () => void;
  onRetake?: () => void;
}

export const ImagePreview: React.FC<ImagePreviewProps> = ({ src, onRemove, onRetake }) => {
  return (
    <div className="relative group glass-effect rounded-xl p-3 space-y-3">
      {/* Image */}
      <div className="relative rounded-lg overflow-hidden bg-input-bg">
        <img 
          src={src} 
          alt="Preview" 
          className="w-full h-32 object-contain"
        />
        
        {/* Overlay controls */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-2">
          <Button
            size="sm"
            variant="secondary"
            className="h-8 w-8 p-0 bg-black/50 border-0 text-white hover:bg-black/70"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          {onRetake && (
            <Button
              size="sm"
              variant="secondary"
              onClick={onRetake}
              className="h-7 px-2 text-xs glass-effect text-foreground hover:bg-button-hover"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Retake
            </Button>
          )}
        </div>
        
        <Button
          size="sm"
          variant="secondary"
          onClick={onRemove}
          className="h-7 w-7 p-0 glass-effect text-text-secondary hover:text-destructive hover:bg-destructive/10"
        >
          <X className="w-3 h-3" />
        </Button>
      </div>
    </div>
  );
};