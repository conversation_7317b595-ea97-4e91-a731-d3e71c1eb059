import React from 'react';
import { Progress } from '@/components/ui/progress';

interface ProgressIndicatorProps {
  stage: 'analyzing' | 'processing' | 'generating' | 'complete';
  progress: number;
}

const stageLabels = {
  analyzing: 'Analyzing image...',
  processing: 'Processing task...',
  generating: 'Generating solution...',
  complete: 'Complete'
};

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ stage, progress }) => {
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className="text-sm text-text-secondary">{stageLabels[stage]}</span>
        <span className="text-xs text-text-secondary">{Math.round(progress)}%</span>
      </div>
      <Progress value={progress} className="h-1.5" />
    </div>
  );
};