@tailwind base;
@tailwind components;
@tailwind utilities;

/* ОтветБот Design System - Apple-style dark theme with premium feel */

@layer base {
  :root {
    /* Core dark palette */
    --background: 227 20% 4%;        /* #0B0B10 */
    --foreground: 219 20% 96%;       /* #F5F7FB */
    --text-secondary: 220 8% 64%;    /* #9AA0A6 */
    
    /* Panel specific */
    --panel-background: 227 20% 4%;  /* #0B0B10 */
    --panel-border: 227 10% 12%;     /* subtle border */
    
    /* Glass/blur effects */
    --glass-background: 227 20% 8%;  /* semi-transparent background */
    --glass-border: 220 15% 20%;     /* glass border */
    
    /* Accent gradient colors */
    --accent-primary: 260 87% 57%;   /* #7C3AED */
    --accent-secondary: 217 91% 60%; /* #2563EB */
    
    /* Status colors */
    --status-success: 142 76% 36%;   /* green */
    --status-error: 0 84% 60%;       /* red */
    --status-warning: 43 96% 56%;    /* amber */
    
    /* Interactive elements */
    --button-secondary: 220 15% 20%; /* glass secondary */
    --button-hover: 220 15% 25%;     /* hover state */
    
    /* Input elements */
    --input-background: 227 15% 8%;
    --input-border: 220 15% 20%;
    --input-focus: 260 87% 57%;
    
    /* Cards and surfaces */
    --card-background: 227 15% 8%;
    --card-border: 220 15% 15%;
    
    /* Overlays */
    --overlay-background: 0 0% 0%;   /* for screenshot overlay */
    --selection-border: 260 87% 57%; /* selection frame */
    --selection-fill: 260 87% 57%;   /* selection background */
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Spacing and sizing */
    --panel-width: 380px;
    --panel-padding: 16px;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    
    /* Typography weights */
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* Animation */
    --animation-fast: 200ms;
    --animation-normal: 250ms;
    --animation-slow: 400ms;
    --easing: cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Legacy mappings for compatibility */
    --primary: 260 87% 57%;
    --primary-foreground: 219 20% 96%;
    --secondary: 220 15% 20%;
    --secondary-foreground: 219 20% 96%;
    --muted: 227 15% 8%;
    --muted-foreground: 220 8% 64%;
    --accent: 217 91% 60%;
    --accent-foreground: 219 20% 96%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 219 20% 96%;
    --border: 220 15% 15%;
    --input: 227 15% 8%;
    --ring: 260 87% 57%;
    --card: 227 15% 8%;
    --card-foreground: 219 20% 96%;
    --popover: 227 15% 8%;
    --popover-foreground: 219 20% 96%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }
}

@layer components {
  /* Gradient utilities */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--accent-primary)), hsl(var(--accent-secondary)));
  }
  
  /* Glass effect */
  .glass-effect {
    background: hsl(var(--glass-background) / 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid hsl(var(--glass-border) / 0.2);
  }
  
  /* Animations */
  .fade-up {
    animation: fadeUp var(--animation-slow) var(--easing);
  }
  
  .hover-lift {
    transition: transform var(--animation-fast) var(--easing);
  }
  
  .hover-lift:hover {
    transform: translateY(-1px);
  }
  
  /* Selection overlay */
  .selection-overlay {
    background: hsl(var(--overlay-background) / 0.15);
    backdrop-filter: blur(2px);
  }
  
  .selection-frame {
    border: 2px solid hsl(var(--selection-border));
    background: hsl(var(--selection-fill) / 0.15);
    box-shadow: 0 4px 20px hsl(var(--selection-border) / 0.3);
  }
}

@layer utilities {
  /* Custom keyframes */
  @keyframes fadeUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes pulse-subtle {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
  
  /* Typography utilities */
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--accent-primary)), hsl(var(--accent-secondary)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* RTL support */
  [dir="rtl"] {
    direction: rtl;
  }
  
  [dir="rtl"] .mr-2 {
    margin-right: 0;
    margin-left: 0.5rem;
  }
  
  [dir="rtl"] .mr-3 {
    margin-right: 0;
    margin-left: 0.75rem;
  }
  
  /* Language-specific adjustments */
  [lang="ar"], [lang="he"] {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
  }
  
  [lang="zh"], [lang="ja"], [lang="ko"] {
    font-family: 'Segoe UI', 'Noto Sans CJK', sans-serif;
  }
}
