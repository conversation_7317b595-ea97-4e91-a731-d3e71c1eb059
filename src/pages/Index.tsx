import { AnswerBotPanel } from '@/components/AnswerBotPanel';

const Index = () => {
  return (
    <div className="min-h-screen bg-background flex">
      {/* Main content area - simulating a web page */}
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center space-y-6 px-8">
          <h1 className="text-4xl font-bold text-foreground">
            Демо страница
          </h1>
          <p className="text-xl text-text-secondary max-w-2xl">
            Это демонстрационная страница для тестирования функций ОтветБот. 
            Используйте панель справа для создания скриншотов, загрузки изображений или ввода задач.
          </p>
          <div className="space-y-4 text-left bg-card-bg p-6 rounded-xl border border-card-border max-w-lg">
            <h3 className="text-lg font-semibold text-foreground">Пример задачи:</h3>
            <p className="text-foreground">
              Решите квадратное уравнение: <strong>x² - 5x + 6 = 0</strong>
            </p>
            <p className="text-text-secondary text-sm">
              Выделите эту область с помощью инструмента скриншота для автоматического решения.
            </p>
          </div>
        </div>
      </div>
      
      {/* ОтветБот Panel */}
      <AnswerBotPanel />
    </div>
  );
};

export default Index;
