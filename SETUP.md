# Инструкция по установке ОтветБот Chrome Extension

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
# Установить зависимости сервера
npm run setup
```

### 2. Настройка OpenRouter API

1. Зарегистрируйтесь на [OpenRouter.ai](https://openrouter.ai)
2. Получите API ключ в разделе "Keys"
3. Откройте файл `server/.env` и добавьте ваш ключ:

```env
OPENROUTER_API_KEY=sk-or-v1-ваш-ключ-здесь
```

### 3. Запуск сервера

```bash
# Запуск в режиме разработки
npm run dev

# Или собрать и запустить для продакшена
npm run build
npm run start:server
```

Сервер будет доступен по адресу: http://localhost:3000

### 4. Установка расширения в Chrome

1. Откройте Chrome
2. Перейдите по адресу: `chrome://extensions/`
3. Включите "Режим разработчика" (Developer mode) в правом верхнем углу
4. Нажмите "Загрузить распакованное расширение" (Load unpacked)
5. Выберите папку `extension` из этого проекта
6. Расширение "ОтветБот" появится в списке

## ✅ Проверка работы

### Проверка сервера:

```bash
curl http://localhost:3000/health
```

Ответ должен быть:
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456,
  "environment": "development"
}
```

### Проверка расширения:

1. Нажмите на иконку расширения в панели Chrome
2. Должен открыться popup с тремя кнопками
3. Статус внизу должен показывать "Готов к работе"

## 🎯 Тестирование функций

### 1. Скриншот области

1. Нажмите на иконку расширения
2. Выберите "Сделать скриншот" или нажмите `Ctrl+Shift+S`
3. Выделите область на странице
4. Дождитесь решения от ИИ

### 2. Загрузка изображения

1. Нажмите на иконку расширения
2. Выберите "Загрузить изображение"
3. Выберите файл с задачей
4. Дождитесь решения от ИИ

### 3. Текстовая задача

1. Нажмите на иконку расширения
2. Выберите "Описать задачу"
3. Введите текст задачи
4. Дождитесь решения от ИИ

## 🔧 Настройка среды разработки

### Переменные окружения

Создайте файл `server/.env` со следующими параметрами:

```env
# Обязательные
OPENROUTER_API_KEY=sk-or-v1-ваш-ключ

# Опциональные
PORT=3000
NODE_ENV=development
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
LOG_LEVEL=info
```

### Логирование

Для просмотра логов:

1. **Сервер**: логи отображаются в консоли где запущен `npm run dev`
2. **Background Script**:
   - Откройте `chrome://extensions/`
   - Найдите ОтветБот → "Подробности" → "Просмотр background page"
   - Откройте Developer Tools
3. **Content Script**:
   - Откройте Developer Tools на любой странице (F12)
   - Вкладка Console

## 🚨 Устранение проблем

### Сервер не запускается

1. Проверьте Node.js версию: `node --version` (требуется ≥18)
2. Удалите node_modules и переустановите: `rm -rf server/node_modules && npm run install:server`
3. Проверьте файл `.env`

### Расширение не работает

1. Проверьте консоль браузера на ошибки
2. Убедитесь, что сервер запущен на порту 3000
3. Перезагрузите расширение в `chrome://extensions/`

### API ошибки

1. Проверьте правильность API ключа OpenRouter
2. Убедитесь, что у вас есть кредиты на OpenRouter
3. Проверьте сетевое соединение

### CORS ошибки

Убедитесь, что в настройках сервера разрешены запросы от Chrome Extension:

```javascript
// В server/src/index.ts уже настроено
const corsOptions = {
  origin: function (origin, callback) {
    if (!origin || origin.startsWith('chrome-extension://')) {
      callback(null, true);
    }
  }
};
```

## 📚 Полезные команды

```bash
# Установка всех зависимостей
npm run install:all

# Запуск в development режиме
npm run dev

# Сборка для продакшена
npm run build

# Проверка статуса сервера
curl http://localhost:3000/health

# Проверка API
curl -X POST http://localhost:3000/api/ai/solve-text \
  -H "Content-Type: application/json" \
  -d '{"text": "2+2=?"}'

# Очистка всех build файлов
npm run clean
```

## 🔄 Обновление

При обновлении кода расширения:

1. Откройте `chrome://extensions/`
2. Найдите ОтветБот
3. Нажмите кнопку "Обновить" (🔄)

При обновлении сервера просто перезапустите его: `Ctrl+C` и снова `npm run dev`

## 🆘 Получение помощи

Если что-то не работает:

1. Проверьте логи сервера и расширения
2. Убедитесь, что все переменные окружения настроены
3. Попробуйте очистить кэш и перезапустить все
4. Создайте Issue в GitHub репозитории с подробным описанием проблемы

Готово! Теперь у вас должно работать полнофункциональное расширение ОтветБот 🎉